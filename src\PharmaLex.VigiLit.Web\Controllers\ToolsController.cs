﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Scraping.Client;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.SuperAdmin)]
[Route("[controller]")]
public class ToolsController : BaseController
{
    private readonly IImportQueueService _importQueueService;
    private readonly IEmailService _emailService;
    private readonly ICaseFilesEmailService _caseFilesEmailService;
    private readonly IVigiLitScrapingClient _scrapingClient;
    private readonly IJournalService _journalService;
    private readonly ILogger<ToolsController> _logger;

    public ToolsController(
        IImportQueueService importQueueService,
        IEmailService emailService,
        ICaseFilesEmailService caseFilesEmailService,
        IVigiLitScrapingClient scrapingClient,
        IJournalService journalService,
        IDataExtractionClient dataExtractionClient,
        IUserSessionService userSessionService,
        IConfiguration configuration,
        ILogger<ToolsController> logger) : base(userSessionService, configuration)
    {
        _importQueueService = importQueueService;
        _emailService = emailService;
        _caseFilesEmailService = caseFilesEmailService;
        _scrapingClient = scrapingClient;
        _journalService = journalService;
        _logger = logger;
    }

    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> EnqueueScheduledImport()
    {
        await _importQueueService.EnqueueScheduledImport(ImportTriggerType.Manual);

        AddNotification("Executed: Enqueue Scheduled Import.", UserNotificationType.Confirm);

        return RedirectToAction("ImportLog", "Import");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> RestoreJournalSchedules()
    {
        var journals = await _journalService.GetAll();
        var enabledJournalsWithSchedules = journals
            .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
            .ToList();

        if (enabledJournalsWithSchedules.Count is 0)
        {
            AddNotification($"No journals found that require restoration.", UserNotificationType.Warning);
        }

        var result = await _scrapingClient.Send(new RestoreSchedulesCommand()
        {
            Journals = enabledJournalsWithSchedules
        });

        if (result.Errors.Count is not 0)
        {
            _logger.LogWarning("Journal schedule restoration completed with {ErrorCount} errors. " +
                "Tasks: {Tasks}, Schedules: {Schedules}, Webhooks: {Webhooks}",
                result.Errors.Count, result.TasksCreated, result.SchedulesCreated, result.WebhooksCreated);

            AddNotification($"Journal schedule restoration completed with {result.Errors.Count} errors. " +
                $"Created - Tasks: {result.TasksCreated}, Schedules: {result.SchedulesCreated}, Webhooks: {result.WebhooksCreated}. " +
                $"Check logs for error details.", UserNotificationType.Warning);
        }
        else
        {
            _logger.LogInformation("Journal schedule restoration completed successfully. " +
                "Tasks: {Tasks}, Schedules: {Schedules}, Webhooks: {Webhooks}, Journals: {Journals}",
                result.TasksCreated, result.SchedulesCreated, result.WebhooksCreated, result.JournalsProcessed);

            AddNotification($"Journal schedule restoration completed successfully. " +
                $"Created - Tasks: {result.TasksCreated}, Schedules: {result.SchedulesCreated}, Webhooks: {result.WebhooksCreated} " +
                $"for {result.JournalsProcessed} journals.", UserNotificationType.Confirm);
        }

        return RedirectToAction("Index");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyReferenceClassificationEmails()
    {
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Reference Classification Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyCaseFilesEmails()
    {
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Case Files Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }
}