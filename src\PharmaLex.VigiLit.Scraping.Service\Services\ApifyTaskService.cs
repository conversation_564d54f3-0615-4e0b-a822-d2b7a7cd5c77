using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyTaskService : IApifyTaskService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyTaskService> _logger;

    public ApifyTaskService(IApifyClient apifyClient, ILogger<ApifyTaskService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateTaskForJournalAsync(JournalScheduleInfo journal, string taskName, CancellationToken cancellationToken = default)
    {
        try
        {
            var urls = new List<string> { journal.Url };
            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 1, cancellationToken);

            _logger.LogInformation("Created Apify task '{TaskName}' with ID '{TaskId}' for journal '{JournalName}'",
                taskName, taskId, journal.Name);

            return taskId;
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify task '{taskName}' for journal '{journal.Name}' (ID: {journal.Id}, URL: {journal.Url})";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
