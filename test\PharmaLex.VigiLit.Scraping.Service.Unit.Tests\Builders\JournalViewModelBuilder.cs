using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Fakes;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;

public class JournalViewModelBuilder : IBuilder<JournalViewModel>
{
    private int id;
    private string name;
    private string? url;
    private string? issn;
    private string cronExpression;
    private bool enabled;
    private int countryId;
    private string countryName;

    public JournalViewModelBuilder()
    {
        this.id = Fake.Journal.Id;
        this.name = Fake.Journal.Name;
        this.url = Fake.Journal.Url;
        this.issn = Fake.Journal.Issn;
        this.cronExpression = Fake.Journal.CronExpression;
        this.enabled = Fake.Journal.Enabled;
        this.countryId = Fake.Journal.CountryId;
        this.countryName = Fake.Journal.CountryName;
    }

    public JournalViewModel Build()
    {
        var journalViewModel = new JournalViewModel
        {
            Id = id,
            Name = name,
            Url = url,
            Issn = issn,
            CronExpression = cronExpression,
            Enabled = enabled,
            CountryId = countryId,
            CountryName = countryName
        };

        return journalViewModel;
    }

    public JournalViewModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public JournalViewModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public JournalViewModelBuilder WithUrl(string? url)
    {
        this.url = url;
        return this;
    }

    public JournalViewModelBuilder WithIssn(string? issn)
    {
        this.issn = issn;
        return this;
    }

    public JournalViewModelBuilder WithCronExpression(string cronExpression)
    {
        this.cronExpression = cronExpression;
        return this;
    }

    public JournalViewModelBuilder WithEnabled(bool enabled)
    {
        this.enabled = enabled;
        return this;
    }

    public JournalViewModelBuilder WithCountryId(int countryId)
    {
        this.countryId = countryId;
        return this;
    }

    public JournalViewModelBuilder WithCountryName(string countryName)
    {
        this.countryName = countryName;
        return this;
    }
}
