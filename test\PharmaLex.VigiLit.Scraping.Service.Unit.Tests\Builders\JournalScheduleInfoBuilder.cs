using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;

public class JournalScheduleInfoBuilder : IBuilder<JournalScheduleInfo>
{
    private int id;
    private string name;
    private string? url;
    private string cronExpression;

    public JournalScheduleInfoBuilder()
    {
        this.id = Fake.Journal.Id;
        this.name = Fake.Journal.Name;
        this.url = Fake.Journal.Url;
        this.cronExpression = Fake.Journal.CronExpression;
    }

    public JournalScheduleInfo Build()
    {
        var journalScheduleInfoModel = new JournalScheduleInfo
        {
            Id = id,
            Name = name,
            Url = url,
            CronExpression = cronExpression
        };

        return journalScheduleInfoModel;
    }

    public JournalScheduleInfoBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public JournalScheduleInfoBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public JournalScheduleInfoBuilder WithUrl(string? url)
    {
        this.url = url;
        return this;
    }

    public JournalScheduleInfoBuilder WithCronExpression(string cronExpression)
    {
        this.cronExpression = cronExpression;
        return this;
    }
}
