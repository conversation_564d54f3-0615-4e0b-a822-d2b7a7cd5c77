﻿using AutoFixture;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public static class Fake
{
    private static readonly Random Random = new();
    public static readonly Fixture fixture = new();

    public static class Journal
    {
        private static readonly Fixture fixture = new();

        public static int Id => fixture.Create<int>();
        public static string Name => GetRandomString(100);
        public static string Url => $"https://{GetRandomString(10).ToLower()}.com";
        public static string Issn => GetRandomString(20);
        public static string CronExpression => "0 9 * * 1";
        public static bool Enabled => fixture.Create<bool>();
        public static int CountryId => fixture.Create<int>();
        public static string CountryName => GetRandomString(50);
        public static string CreatedBy => fixture.Create<string>();
        public static string LastUpdatedBy => fixture.Create<string>();
    }

    public static string GetRandomString(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }

}
