using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class ApifyScheduleServiceTests
{
    private readonly Mock<IApifyClient> _apifyClientMock;
    private readonly Mock<ILogger<ApifyScheduleService>> _loggerMock;
    private readonly ApifyScheduleService _sut;

    public ApifyScheduleServiceTests()
    {
        _apifyClientMock = new Mock<IApifyClient>();
        _loggerMock = new Mock<ILogger<ApifyScheduleService>>();
        _sut = new ApifyScheduleService(_apifyClientMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CreateScheduleForTaskAsync_WhenSuccessful_CompletesWithoutException()
    {
        // Arrange
        var taskId = Fake.GetRandomString(5);
        var scheduleName = Fake.GetRandomString(10);
        var cronExpression = "0 9 * * 1";

        _apifyClientMock.Setup(x => x.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Apify.SDK.Model.ScheduleResponse(new Apify.SDK.Model.ScheduleResponseData(
                id: Fake.GetRandomString(6),
                userId:Fake.GetRandomString(8),
                name: scheduleName,
                cronExpression: cronExpression,
                timezone: "UTC",
                isEnabled: true,
                isExclusive: false,
                description: Fake.GetRandomString(10),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
                actions: [])));

        // Act & Assert
        await _sut.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression);

        _apifyClientMock.Verify(x => x.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", It.IsAny<CancellationToken>()), Times.Once);

        VerifyLogCalled(LogLevel.Information, "Created Apify schedule");
    }

    [Fact]
    public async Task CreateScheduleForTaskAsync_WhenApifyClientThrows_ThrowsInvalidOperationException()
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var scheduleName = Fake.GetRandomString(15);
        var cronExpression = "0 9 * * 1";
        var originalException = new Exception("Apify API error");

        _apifyClientMock.Setup(x => x.CreateSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(originalException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _sut.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression));

        Assert.Contains($"Failed to create Apify schedule '{scheduleName}' for task '{taskId}' with cron expression '{cronExpression}'", exception.Message);
        Assert.Equal(originalException, exception.InnerException);

        VerifyLogCalled(LogLevel.Error, "Failed to create Apify schedule");
    }

    [Fact]
    public async Task CreateScheduleForTaskAsync_WithCancellationToken_PassesToApifyClient()
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var scheduleName = Fake.GetRandomString(12);
        var cronExpression = "0 10 * * 2";
        var cancellationToken = new CancellationToken();

        _apifyClientMock.Setup(x => x.CreateSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), "UTC", cancellationToken))
            .ReturnsAsync(new Apify.SDK.Model.ScheduleResponse(new Apify.SDK.Model.ScheduleResponseData(
                id: Fake.GetRandomString(10),
                userId: Fake.GetRandomString(8),
                name: scheduleName,
                cronExpression: cronExpression,
                timezone: "UTC",
                isEnabled: true,
                isExclusive: false,
                description: Fake.GetRandomString(15),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
                lastRunAt: string.Empty,
                actions: new List<Apify.SDK.Model.ScheduleResponseDataActions>())));

        // Act
        await _sut.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression, cancellationToken);

        // Assert
        _apifyClientMock.Verify(x => x.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", cancellationToken), Times.Once);
    }

    [Theory]
    [InlineData("0 9 * * 1", "Every Monday at 9 AM")]
    [InlineData("0 10 * * 2", "Every Tuesday at 10 AM")]
    [InlineData("0 0 1 * *", "First day of every month at midnight")]
    [InlineData("*/15 * * * *", "Every 15 minutes")]
    public async Task CreateScheduleForTaskAsync_WithVariousCronExpressions_CallsApifyClientCorrectly(string cronExpression, string description)
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var scheduleName = Fake.GetRandomString(12);

        _apifyClientMock.Setup(x => x.CreateSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), "UTC", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Apify.SDK.Model.ScheduleResponse(new Apify.SDK.Model.ScheduleResponseData(
                id: Fake.GetRandomString(10),
                userId: Fake.GetRandomString(8),
                name: scheduleName,
                cronExpression: cronExpression,
                timezone: "UTC",
                isEnabled: true,
                isExclusive: false,
                description: Fake.GetRandomString(15),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("O"),
                lastRunAt: string.Empty,
                actions: new List<Apify.SDK.Model.ScheduleResponseDataActions>())));

        // Act
        await _sut.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression);

        // Assert
        _apifyClientMock.Verify(x => x.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", It.IsAny<CancellationToken>()), Times.Once);
        VerifyLogCalled(LogLevel.Information, $"Created Apify schedule '{scheduleName}' for task '{taskId}'");
    }

    private void VerifyLogCalled(LogLevel level, string message)
    {
        _loggerMock.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }
}
