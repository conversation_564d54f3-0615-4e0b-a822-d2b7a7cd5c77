using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;

public class JournalBuilder : IBuilder<Journal>
{
    private string name;
    private string? url;
    private string? issn;
    private string cronExpression;
    private bool enabled;
    private int countryId;
    private DateTime createdDate;
    private DateTime lastUpdatedDate;
    private string lastUpdatedBy;
    private string createdBy;

    public JournalBuilder()
    {
        this.name = Fake.Journal.Name;
        this.url = Fake.Journal.Url;
        this.issn = Fake.Journal.Issn;
        this.cronExpression = Fake.Journal.CronExpression;
        this.enabled = Fake.Journal.Enabled;
        this.countryId = Fake.Journal.CountryId;
        this.createdDate = DateTime.Now;
        this.lastUpdatedDate = DateTime.Now;
        this.lastUpdatedBy = Fake.Journal.LastUpdatedBy;
        this.createdBy = Fake.Journal.CreatedBy;
    }

    public Journal Build()
    {
        var journal = new Journal
        {
            Name = name,
            Url = url,
            Issn = issn,
            CronExpression = cronExpression,
            Enabled = enabled,
            CountryId = countryId,
            CreatedDate = createdDate,
            LastUpdatedDate = lastUpdatedDate,
            LastUpdatedBy = lastUpdatedBy,
            CreatedBy = createdBy
        };

        return journal;
    }

    public JournalBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public JournalBuilder WithUrl(string? url)
    {
        this.url = url;
        return this;
    }

    public JournalBuilder WithIssn(string? issn)
    {
        this.issn = issn;
        return this;
    }

    public JournalBuilder WithCronExpression(string cronExpression)
    {
        this.cronExpression = cronExpression;
        return this;
    }

    public JournalBuilder WithEnabled(bool enabled)
    {
        this.enabled = enabled;
        return this;
    }

    public JournalBuilder WithCountryId(int countryId)
    {
        this.countryId = countryId;
        return this;
    }

    public JournalBuilder WithCreatedBy(string createdBy)
    {
        this.createdBy = createdBy;
        return this;
    }

    public JournalBuilder WithLastUpdatedBy(string lastUpdatedBy)
    {
        this.lastUpdatedBy = lastUpdatedBy;
        return this;
    }

    public JournalBuilder WithCreatedDate(DateTime createdDate)
    {
        this.createdDate = createdDate;
        return this;
    }

    public JournalBuilder WithLastUpdatedDate(DateTime lastUpdatedDate)
    {
        this.lastUpdatedDate = lastUpdatedDate;
        return this;
    }
}
