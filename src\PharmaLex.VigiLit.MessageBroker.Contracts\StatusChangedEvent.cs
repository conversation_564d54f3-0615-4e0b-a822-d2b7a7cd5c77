﻿namespace PharmaLex.VigiLit.MessageBroker.Contracts;

public class StatusChangedEvent
{
    public StatusChangedEvent(string eventSourceName, Guid correlationId, DateTime timeCreated, string user,
        string message)
    {
        EventSourceName = eventSourceName;
        CorrelationId = correlationId;
        TimeCreated = timeCreated;
        User = user;
        Message = message;
    }

    private string EventSourceName { get; set; }
    private Guid CorrelationId { get; set; }
    private DateTime TimeCreated { get; set; }
    private string User { get; set; }
    private string Message { get; set; }
}