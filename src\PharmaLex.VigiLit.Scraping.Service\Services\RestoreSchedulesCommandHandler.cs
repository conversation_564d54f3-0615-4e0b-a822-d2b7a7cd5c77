using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _taskService = taskService;
        _scheduleService = scheduleService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        foreach (var journal in journals)
        {
            try
            {
                await ProcessJournal(journal, cronExpression, webhookUrl, result, cancellationToken);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing journal '{journal.Name}': {ex.Message}";
                _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                result.Errors.Add(errorMessage);
            }
        }
    }

    private async Task ProcessJournal(
        JournalScheduleInfo journal,
        string cronExpression,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var taskName = $"vigilit-{SanitizeApifyName(journal.Name)}-{journal.Id}";
        var scheduleName = $"schedule-{SanitizeApifyName(journal.Name)}-{journal.Id}";

        _logger.LogInformation("Processing journal '{Name}' (ID: {Id})", journal.Name, journal.Id);

        try
        {
            // Create task
            var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                var errorMessage = $"Failed to create task for journal '{journal.Name}': Task ID was null or empty";
                _logger.LogWarning(LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                result.Errors.Add(errorMessage);
                return;
            }

            result.TasksCreated++;
            result.Messages.Add($"Created task '{taskName}' for journal '{journal.Name}'");

            // Create schedule
            await _scheduleService.CreateScheduleForTaskAsync(taskId, scheduleName, cronExpression, cancellationToken);
            result.SchedulesCreated++;
            result.Messages.Add($"Created schedule '{scheduleName}' for task '{taskId}'");

            // Create webhook
            await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
            result.WebhooksCreated++;
            result.Messages.Add($"Created webhook for task '{taskId}'");

            _logger.LogInformation("Successfully processed journal '{Name}' (ID: {Id}) - Task: {TaskId}",
                journal.Name, journal.Id, taskId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process journal '{journal.Name}' (ID: {journal.Id}): {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }

    private static string SanitizeApifyName(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return "unknown";
        }

        var sanitized = input.ToLowerInvariant();

        var validChars = sanitized.ToCharArray()
            .Select(c => char.IsLetterOrDigit(c) ? c : '-')
            .ToArray();

        sanitized = new string(validChars);
        sanitized = sanitized.Trim('-');

        while (sanitized.Contains("--"))
        {
            sanitized = sanitized.Replace("--", "-");
        }

        sanitized = sanitized.Trim('-');

        return string.IsNullOrEmpty(sanitized) ? "unknown" : sanitized;
    }
}
