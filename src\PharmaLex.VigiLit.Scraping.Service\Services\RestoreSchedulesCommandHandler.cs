using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _taskService = taskService;
        _scheduleService = scheduleService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        // Create a single group task for all journals with the same cron expression
        var groupTaskName = $"vigilit-group-{SanitizeApifyName(cronExpression)}-{DateTime.UtcNow:yyyyMMddHHmmss}";
        var groupScheduleName = $"schedule-group-{SanitizeApifyName(cronExpression)}";

        try
        {
            // Create one group task for all journals in this cron group
            var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);
            if (string.IsNullOrEmpty(groupTaskId))
            {
                var errorMessage = $"Failed to create group task for cron '{cronExpression}': Task ID was null or empty";
                _logger.LogWarning(LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                result.Errors.Add(errorMessage);
                return;
            }

            result.TasksCreated++;
            var journalNames = string.Join(", ", journals.Select(j => j.Name));
            result.Messages.Add($"Created group task '{groupTaskName}' for {journals.Count} journals: {journalNames}");

            // Create one schedule for the group task
            await _scheduleService.CreateScheduleForTaskAsync(groupTaskId, groupScheduleName, cronExpression, cancellationToken);
            result.SchedulesCreated++;
            result.Messages.Add($"Created group schedule '{groupScheduleName}' for cron '{cronExpression}' with {journals.Count} journals");

            // Create one webhook for the group task
            await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
            result.WebhooksCreated++;
            result.Messages.Add($"Created webhook for group task '{groupTaskId}' covering {journals.Count} journals");

            _logger.LogInformation("Successfully processed schedule group for cron '{CronExpression}' with {JournalCount} journals - Group Task: {GroupTaskId}",
                cronExpression, journals.Count, groupTaskId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process schedule group for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }



    private static string SanitizeApifyName(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return "unknown";
        }

        var sanitized = input.ToLowerInvariant();

        var validChars = sanitized.ToCharArray()
            .Select(c => char.IsLetterOrDigit(c) ? c : '-')
            .ToArray();

        sanitized = new string(validChars);
        sanitized = sanitized.Trim('-');

        while (sanitized.Contains("--"))
        {
            sanitized = sanitized.Replace("--", "-");
        }

        sanitized = sanitized.Trim('-');

        return string.IsNullOrEmpty(sanitized) ? "unknown" : sanitized;
    }
}
